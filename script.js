// 全局变量存储用户选择
let selectedMaps = [];
let selectedOperators = [];
let selectedEquipment = [];
let selectedWeaponTypes = [];

// 雷达扫描相关变量
let radarDots = [];
let radarScanInterval;

// 抽取状态标志
let finalResultsShown = false;

// 历史抽取记录，防止连续三次相同
let drawHistory = {
    map: [],
    operator: [],
    helmet: [],
    armor: [],
    backpack: [],
    vest: [],
    weapon: []
};

// 雷达扫描功能
function initializeRadarEffect() {
    // 每4秒执行一次雷达扫描（与CSS动画同步）
    radarScanInterval = setInterval(() => {
        performRadarScan();
    }, 4000);

    // 立即执行第一次扫描
    performRadarScan();
}

function performRadarScan() {
    // 清除旧的雷达点
    clearOldRadarDots();

    // 生成新的随机雷达点
    generateRandomRadarDots();
}

function generateRandomRadarDots() {
    const numberOfDots = Math.floor(Math.random() * 30) + 25; // 25-54个点，大幅增加

    for (let i = 0; i < numberOfDots; i++) {
        // 模拟雷达扫描：点在扫描线经过时出现
        setTimeout(() => {
            createRadarDot();
        }, Math.random() * 3500); // 在扫描过程中随机时间出现
    }
}

function createRadarDot() {
    const dot = document.createElement('div');
    dot.className = 'radar-dot';

    // 随机大小，包含一些特别大的点
    let size;
    const random = Math.random();
    if (random < 0.1) {
        // 10%的概率生成特大点 (35-50px)
        size = Math.floor(Math.random() * 16) + 35;
    } else if (random < 0.3) {
        // 20%的概率生成大点 (25-35px)
        size = Math.floor(Math.random() * 11) + 25;
    } else {
        // 70%的概率生成普通点 (8-25px)
        size = Math.floor(Math.random() * 18) + 8;
    }
    dot.style.width = size + 'px';
    dot.style.height = size + 'px';

    // 随机位置
    const x = Math.random() * (window.innerWidth - size);
    const y = Math.random() * (window.innerHeight - size);
    dot.style.left = x + 'px';
    dot.style.top = y + 'px';

    // 添加到页面
    document.body.appendChild(dot);
    radarDots.push(dot);

    // 真实雷达效果：点在扫描后立即开始缓慢消失
    const fadeStartTime = 2000 + Math.random() * 3000; // 2-5秒后开始消失
    setTimeout(() => {
        if (dot.parentNode) {
            dot.classList.add('fade-out');
            setTimeout(() => {
                if (dot.parentNode) {
                    document.body.removeChild(dot);
                    const index = radarDots.indexOf(dot);
                    if (index > -1) {
                        radarDots.splice(index, 1);
                    }
                }
            }, 3000); // 等待3秒淡出动画完成
        }
    }, fadeStartTime);
}

function clearOldRadarDots() {
    // 移除超过60个的旧点，保持性能
    while (radarDots.length > 60) {
        const oldDot = radarDots.shift();
        if (oldDot.parentNode) {
            oldDot.classList.add('fade-out');
            setTimeout(() => {
                if (oldDot.parentNode) {
                    document.body.removeChild(oldDot);
                }
            }, 3000); // 等待新的淡出动画时间
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 先加载保存的数据
    loadSavedData();

    initializeMapSelection();
    initializeOperatorSelection();
    initializeEquipmentSelection();
    initializeWeaponSelection();
    initializeDrawButton();
    initializeSelectAllButtons();

    // 初始化雷达效果
    initializeRadarEffect();
});

// 数据持久化功能
function saveDataToStorage() {
    const dataToSave = {
        mapData: mapData,
        operatorData: operatorData,
        equipmentData: equipmentData,
        weaponData: weaponData
    };

    try {
        localStorage.setItem('deltaForceRandomizerData', JSON.stringify(dataToSave));
        console.log('数据已保存到本地存储');
    } catch (error) {
        console.error('保存数据失败:', error);
    }
}

function loadSavedData() {
    try {
        const savedData = localStorage.getItem('deltaForceRandomizerData');
        if (savedData) {
            const parsedData = JSON.parse(savedData);

            // 合并保存的数据到现有数据中
            if (parsedData.mapData) {
                Object.assign(mapData, parsedData.mapData);
            }

            if (parsedData.operatorData) {
                // 合并干员数据，避免重复
                parsedData.operatorData.forEach(newOperator => {
                    const exists = operatorData.some(existing => existing.name === newOperator.name);
                    if (!exists) {
                        operatorData.push(newOperator);
                    }
                });
            }

            if (parsedData.equipmentData) {
                Object.keys(parsedData.equipmentData).forEach(category => {
                    if (!equipmentData[category]) {
                        equipmentData[category] = [];
                    }

                    parsedData.equipmentData[category].forEach(newEquipment => {
                        const exists = equipmentData[category].some(existing => existing.name === newEquipment.name);
                        if (!exists) {
                            equipmentData[category].push(newEquipment);
                        }
                    });
                });
            }

            if (parsedData.weaponData) {
                Object.keys(parsedData.weaponData).forEach(category => {
                    if (!weaponData[category]) {
                        weaponData[category] = [];
                    }

                    parsedData.weaponData[category].forEach(newWeapon => {
                        const exists = weaponData[category].some(existing => existing.name === newWeapon.name);
                        if (!exists) {
                            weaponData[category].push(newWeapon);
                        }
                    });
                });
            }

            console.log('已加载保存的数据');
        }
    } catch (error) {
        console.error('加载保存的数据失败:', error);
    }
}

function clearSavedData() {
    if (confirm('确定要清除所有自定义添加的数据吗？这将删除您添加的所有地图、干员、装备和枪械，此操作不可撤销！')) {
        try {
            localStorage.removeItem('deltaForceRandomizerData');
            alert('自定义数据已清除！页面将刷新以重置到初始状态。');
            location.reload();
        } catch (error) {
            console.error('清除数据失败:', error);
            alert('清除数据失败，请重试。');
        }
    }
}

// 删除物品功能
function deleteItem(category, itemName, subcategory, event) {
    // 阻止事件冒泡，避免触发选择事件
    event.stopPropagation();

    if (!confirm(`确定要删除 "${itemName}" 吗？此操作不可撤销！`)) {
        return;
    }

    try {
        switch (category) {
            case 'map':
                deleteMapItem(itemName, subcategory);
                break;
            case 'operator':
                deleteOperatorItem(itemName);
                break;
            case 'equipment':
                deleteEquipmentItem(itemName, subcategory);
                break;
            case 'weapon':
                deleteWeaponItem(itemName, subcategory);
                break;
            default:
                alert('未知的物品类型！');
                return;
        }

        // 保存更新后的数据
        saveDataToStorage();

        // 重新加载对应的选择界面
        reloadSelectionInterface(category);

        alert('物品删除成功！');

    } catch (error) {
        console.error('删除物品时出错:', error);
        alert('删除物品失败，请重试！');
    }
}

function deleteMapItem(mapName, difficulty) {
    if (mapData[mapName]) {
        // 如果地图只有一个难度，删除整个地图
        if (mapData[mapName].difficulties.length === 1) {
            delete mapData[mapName];
        } else {
            // 否则只删除指定难度
            mapData[mapName].difficulties = mapData[mapName].difficulties.filter(d => d !== difficulty);
        }
    }
}

function deleteOperatorItem(operatorName) {
    const index = operatorData.findIndex(op => op.name === operatorName);
    if (index !== -1) {
        operatorData.splice(index, 1);
    }
}

function deleteEquipmentItem(equipmentName, category) {
    if (equipmentData[category]) {
        const index = equipmentData[category].findIndex(eq => eq.name === equipmentName);
        if (index !== -1) {
            equipmentData[category].splice(index, 1);
        }
    }
}

function deleteWeaponItem(weaponName, category) {
    if (weaponData[category]) {
        const index = weaponData[category].findIndex(wp => wp.name === weaponName);
        if (index !== -1) {
            weaponData[category].splice(index, 1);
        }
    }
}

// 管理面板功能
function toggleAdminPanel() {
    const panel = document.getElementById('admin-panel');
    if (panel.style.display === 'none') {
        panel.style.display = 'block';
        // 重置表单
        resetAdminForm();
    } else {
        panel.style.display = 'none';
    }
}

function resetAdminForm() {
    document.getElementById('item-category').value = '';
    document.getElementById('item-name').value = '';
    document.getElementById('item-image').value = '';
    document.getElementById('item-quality').value = '白';
    document.getElementById('weapon-type').value = '手枪';

    // 重置图片预览
    document.getElementById('image-preview').style.display = 'none';
    document.getElementById('preview-img').src = '';

    // 重置难度复选框
    const checkboxes = document.querySelectorAll('.difficulty-checkboxes input[type="checkbox"]');
    checkboxes.forEach(cb => cb.checked = false);

    // 隐藏所有条件字段
    document.getElementById('quality-group').style.display = 'none';
    document.getElementById('difficulty-group').style.display = 'none';
    document.getElementById('weapon-type-group').style.display = 'none';
}

function updateCategoryFields() {
    const category = document.getElementById('item-category').value;
    const qualityGroup = document.getElementById('quality-group');
    const difficultyGroup = document.getElementById('difficulty-group');
    const weaponTypeGroup = document.getElementById('weapon-type-group');

    // 隐藏所有字段
    qualityGroup.style.display = 'none';
    difficultyGroup.style.display = 'none';
    weaponTypeGroup.style.display = 'none';

    // 根据分类显示对应字段
    if (['helmet', 'armor', 'backpack', 'vest'].includes(category)) {
        qualityGroup.style.display = 'block';
    } else if (category === 'map') {
        difficultyGroup.style.display = 'block';
    } else if (category === 'weapon') {
        weaponTypeGroup.style.display = 'block';
    }
}

// 图片预览功能
function previewImage(input) {
    const file = input.files[0];
    const preview = document.getElementById('image-preview');
    const previewImg = document.getElementById('preview-img');

    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            previewImg.src = e.target.result;
            preview.style.display = 'block';
        };
        reader.readAsDataURL(file);
    } else {
        preview.style.display = 'none';
        previewImg.src = '';
    }
}

// 将图片文件转换为Base64或保存到本地路径
function processImageFile(file, category, name) {
    return new Promise((resolve, reject) => {
        if (!file) {
            reject('没有选择图片文件');
            return;
        }

        // 创建文件路径（模拟保存到对应文件夹）
        const categoryFolders = {
            'map': '地图图片',
            'operator': '干员图片',
            'helmet': '装备/头盔',
            'armor': '装备/护甲',
            'backpack': '装备/背包',
            'vest': '装备/胸挂',
            'weapon': '枪械图片'
        };

        const folder = categoryFolders[category] || '其他图片';
        const fileName = `${name}.${file.name.split('.').pop()}`;
        const imagePath = `${folder}/${fileName}`;

        // 在实际应用中，这里应该上传文件到服务器
        // 现在我们使用Base64作为临时解决方案
        const reader = new FileReader();
        reader.onload = function(e) {
            resolve({
                path: imagePath,
                dataUrl: e.target.result
            });
        };
        reader.onerror = function() {
            reject('读取图片文件失败');
        };
        reader.readAsDataURL(file);
    });
}

function addNewItem() {
    const category = document.getElementById('item-category').value;
    const name = document.getElementById('item-name').value.trim();
    const imageFile = document.getElementById('item-image').files[0];

    if (!category || !name || !imageFile) {
        alert('请填写完整的物品信息并上传图片！');
        return;
    }

    // 处理图片文件
    processImageFile(imageFile, category, name)
        .then(imageInfo => {
            try {
                switch (category) {
                    case 'map':
                        addNewMap(name, imageInfo.dataUrl);
                        break;
                    case 'operator':
                        addNewOperator(name, imageInfo.dataUrl);
                        break;
                    case 'helmet':
                    case 'armor':
                    case 'backpack':
                    case 'vest':
                        addNewEquipment(category, name, imageInfo.dataUrl);
                        break;
                    case 'weapon':
                        addNewWeapon(name, imageInfo.dataUrl);
                        break;
                    default:
                        alert('未知的分类类型！');
                        return;
                }

                alert('物品添加成功！');

                // 保存数据到本地存储
                saveDataToStorage();

                toggleAdminPanel();

                // 重新加载对应的选择界面
                reloadSelectionInterface(category);

            } catch (error) {
                console.error('添加物品时出错:', error);
                alert('添加物品失败，请检查输入信息！');
            }
        })
        .catch(error => {
            console.error('处理图片时出错:', error);
            alert('图片处理失败：' + error);
        });
}

function addNewMap(name, image) {
    // 获取选中的难度
    const checkboxes = document.querySelectorAll('.difficulty-checkboxes input[type="checkbox"]:checked');
    const difficulties = Array.from(checkboxes).map(cb => cb.value);

    if (difficulties.length === 0) {
        alert('请至少选择一个地图难度！');
        return;
    }

    // 添加到地图数据
    mapData[name] = {
        difficulties: difficulties,
        image: image
    };
}

function addNewOperator(name, image) {
    // 添加到干员数据
    operatorData.push({
        name: name,
        image: image
    });
}

function addNewEquipment(category, name, image) {
    const quality = document.getElementById('item-quality').value;

    // 获取装备类型的中文名
    const categoryMap = {
        'helmet': '头盔',
        'armor': '护甲',
        'backpack': '背包',
        'vest': '胸挂'
    };

    const categoryName = categoryMap[category];

    // 添加到装备数据
    if (!equipmentData[categoryName]) {
        equipmentData[categoryName] = [];
    }

    equipmentData[categoryName].push({
        name: name,
        quality: quality,
        image: image
    });
}

function addNewWeapon(name, image) {
    const weaponType = document.getElementById('weapon-type').value;

    // 添加到枪械数据
    if (!weaponData[weaponType]) {
        weaponData[weaponType] = [];
    }

    weaponData[weaponType].push({
        name: name,
        image: image
    });
}

function reloadSelectionInterface(category) {
    // 根据分类重新加载对应的选择界面
    switch (category) {
        case 'map':
            initializeMapSelection();
            break;
        case 'operator':
            initializeOperatorSelection();
            break;
        case 'helmet':
        case 'armor':
        case 'backpack':
        case 'vest':
        case 'equipment':
            initializeEquipmentSelection();
            break;
        case 'weapon':
            initializeWeaponSelection();
            break;
    }
}

// 初始化地图选择模块
function initializeMapSelection() {
    const mapGrid = document.getElementById('map-grid');

    // 清空现有内容
    mapGrid.innerHTML = '';

    Object.keys(mapData).forEach(mapName => {
        const mapInfo = mapData[mapName];
        
        // 为每个地图创建选择项
        mapInfo.difficulties.forEach(difficulty => {
            const mapItem = document.createElement('div');
            mapItem.className = 'selection-item map-item';
            mapItem.dataset.map = mapName;
            mapItem.dataset.difficulty = difficulty;
            
            mapItem.innerHTML = `
                <div class="delete-btn" onclick="deleteItem('map', '${mapName}', '${difficulty}', event)"></div>
                <img src="${mapInfo.image}" alt="${mapName}" onerror="this.style.display='none'">
                <div class="name">${mapName}</div>
                <div class="difficulty">${difficulty}</div>
            `;
            
            mapItem.addEventListener('click', function() {
                toggleMapSelection(this);
            });
            
            mapGrid.appendChild(mapItem);
        });
    });
}

// 切换地图选择状态
function toggleMapSelection(element) {
    const mapName = element.dataset.map;
    const difficulty = element.dataset.difficulty;
    const mapKey = `${mapName}-${difficulty}`;
    
    if (element.classList.contains('selected')) {
        element.classList.remove('selected');
        selectedMaps = selectedMaps.filter(item => item !== mapKey);
    } else {
        element.classList.add('selected');
        selectedMaps.push(mapKey);
    }
    
    console.log('选中的地图:', selectedMaps);
}

// 初始化干员选择模块
function initializeOperatorSelection() {
    const operatorGrid = document.getElementById('operator-grid');

    // 清空现有内容
    operatorGrid.innerHTML = '';

    operatorData.forEach(operator => {
        const operatorItem = document.createElement('div');
        operatorItem.className = 'selection-item operator-item';
        operatorItem.dataset.operator = operator.name;
        
        operatorItem.innerHTML = `
            <div class="delete-btn" onclick="deleteItem('operator', '${operator.name}', null, event)"></div>
            <img src="${operator.image}" alt="${operator.name}" onerror="this.style.display='none'">
            <div class="name">${operator.name}</div>
        `;
        
        operatorItem.addEventListener('click', function() {
            toggleOperatorSelection(this);
        });
        
        operatorGrid.appendChild(operatorItem);
    });
}

// 切换干员选择状态
function toggleOperatorSelection(element) {
    const operatorName = element.dataset.operator;
    
    if (element.classList.contains('selected')) {
        element.classList.remove('selected');
        selectedOperators = selectedOperators.filter(item => item !== operatorName);
    } else {
        element.classList.add('selected');
        selectedOperators.push(operatorName);
    }
    
    console.log('选中的干员:', selectedOperators);
}

// 初始化装备选择模块
function initializeEquipmentSelection() {
    // 初始化头盔
    initializeEquipmentCategory('helmet-grid', '头盔');

    // 初始化护甲
    initializeEquipmentCategory('armor-grid', '护甲');

    // 初始化背包
    initializeEquipmentCategory('backpack-grid', '背包');

    // 初始化胸挂
    initializeEquipmentCategory('vest-grid', '胸挂');
}

// 初始化特定装备类别
function initializeEquipmentCategory(gridId, categoryName) {
    const grid = document.getElementById(gridId);
    const equipmentList = equipmentData[categoryName];

    if (!equipmentList) return;

    // 清空现有内容
    grid.innerHTML = '';

    equipmentList.forEach(equipment => {
        const equipmentItem = document.createElement('div');
        equipmentItem.className = 'selection-item equipment-item';
        equipmentItem.dataset.equipmentId = `${categoryName}-${equipment.name}`;

        // 根据品质设置边框颜色
        const qualityColors = {
            '白': '#ffffff',
            '绿': '#4CAF50',
            '蓝': '#2196F3',
            '紫': '#9C27B0',
            '金': '#FF9800',
            '红': '#F44336'
        };

        equipmentItem.style.borderColor = qualityColors[equipment.quality] || '#ffffff';

        equipmentItem.innerHTML = `
            <div class="delete-btn" onclick="deleteItem('equipment', '${equipment.name}', '${categoryName}', event)"></div>
            <img src="${equipment.image}" alt="${equipment.name}" onerror="this.style.display='none'">
            <div class="name">${equipment.name}</div>
            <div class="quality" style="color: ${qualityColors[equipment.quality]}">${equipment.quality}色</div>
        `;

        equipmentItem.addEventListener('click', function() {
            toggleEquipmentSelection(this, equipment);
        });

        grid.appendChild(equipmentItem);
    });
}

// 切换装备选择状态
function toggleEquipmentSelection(element, equipment) {
    const equipmentId = element.dataset.equipmentId;

    if (element.classList.contains('selected')) {
        element.classList.remove('selected');
        selectedEquipment = selectedEquipment.filter(item => item.id !== equipmentId);
    } else {
        element.classList.add('selected');
        selectedEquipment.push({
            id: equipmentId,
            name: equipment.name,
            quality: equipment.quality,
            image: equipment.image,
            category: equipmentId.split('-')[0]
        });
    }

    console.log('选中的装备:', selectedEquipment);
}

// 初始化枪械选择模块
function initializeWeaponSelection() {
    // 初始化各种枪械类型
    initializeWeaponCategory('pistol-grid', '手枪');
    initializeWeaponCategory('smg-grid', '冲锋枪');
    initializeWeaponCategory('rifle-grid', '步枪');
    initializeWeaponCategory('marksman-grid', '射手步枪');
    initializeWeaponCategory('sniper-grid', '狙击步枪');
    initializeWeaponCategory('lmg-grid', '机枪');
    initializeWeaponCategory('shotgun-grid', '霰弹枪');
}

// 初始化特定枪械类别
function initializeWeaponCategory(gridId, categoryName) {
    const grid = document.getElementById(gridId);
    const weaponList = weaponData[categoryName];

    if (!weaponList) return;

    // 清空现有内容
    grid.innerHTML = '';

    weaponList.forEach(weapon => {
        const weaponItem = document.createElement('div');
        weaponItem.className = 'selection-item weapon-item';
        weaponItem.dataset.weaponId = `${categoryName}-${weapon.name}`;

        weaponItem.innerHTML = `
            <div class="delete-btn" onclick="deleteItem('weapon', '${weapon.name}', '${categoryName}', event)"></div>
            <img src="${weapon.image}" alt="${weapon.name}" onerror="this.style.display='none'">
            <div class="name">${weapon.name}</div>
        `;

        weaponItem.addEventListener('click', function() {
            toggleWeaponSelection(this);
        });

        grid.appendChild(weaponItem);
    });
}

// 切换枪械选择状态
function toggleWeaponSelection(element) {
    const weaponId = element.dataset.weaponId;

    console.log('点击枪械:', weaponId);
    console.log('当前是否已选中:', element.classList.contains('selected'));

    if (element.classList.contains('selected')) {
        element.classList.remove('selected');
        selectedWeaponTypes = selectedWeaponTypes.filter(item => item !== weaponId);
        console.log('取消选择枪械:', weaponId);
    } else {
        element.classList.add('selected');
        selectedWeaponTypes.push(weaponId);
        console.log('选择枪械:', weaponId);
    }

    console.log('当前选中的所有枪械:', selectedWeaponTypes);
}

// 初始化抽取按钮
function initializeDrawButton() {
    const drawButton = document.getElementById('draw-button');
    drawButton.addEventListener('click', startDraw);
}

// 开始抽取
function startDraw() {
    // 调试信息：显示当前选择状态
    console.log('=== 抽取前检查 ===');
    console.log('选中地图数量:', selectedMaps.length, selectedMaps);
    console.log('选中干员数量:', selectedOperators.length, selectedOperators);
    console.log('选中装备数量:', selectedEquipment.length, selectedEquipment);
    console.log('选中枪械数量:', selectedWeaponTypes.length, selectedWeaponTypes);

    // 检查是否有选择
    if (selectedMaps.length === 0 && selectedOperators.length === 0 &&
        selectedEquipment.length === 0 && selectedWeaponTypes.length === 0) {
        alert('请至少选择一个类别的选项！');
        return;
    }

    // 重置状态标志
    finalResultsShown = false;

    // 先生成最终结果
    const finalResults = {
        map: getRandomMap(),
        operator: getRandomOperator(),
        helmet: getRandomEquipmentByType('头盔'),
        armor: getRandomEquipmentByType('护甲'),
        backpack: getRandomEquipmentByType('背包'),
        vest: getRandomEquipmentByType('胸挂'),
        weapon: getRandomWeapon()
    };

    console.log('=== 预生成的最终结果 ===');
    console.log('地图:', finalResults.map);
    console.log('干员:', finalResults.operator);
    console.log('头盔:', finalResults.helmet);
    console.log('护甲:', finalResults.armor);
    console.log('背包:', finalResults.backpack);
    console.log('胸挂:', finalResults.vest);
    console.log('枪械:', finalResults.weapon);

    // 禁用抽取按钮
    const drawButton = document.getElementById('draw-button');
    drawButton.disabled = true;
    drawButton.textContent = '抽取中...';

    // 开始老虎机动画，传入最终结果
    startSlotMachineAnimation(finalResults);

    // 3.5秒后停止动画
    setTimeout(() => {
        stopSlotMachineAnimation();

        // 设置标志，防止后续覆盖
        finalResultsShown = true;
        console.log('动画结束，最终结果已确定');

        // 重新启用按钮
        drawButton.disabled = false;
        drawButton.textContent = '开始抽取';
    }, 3500);
}

// 开始老虎机动画
function startSlotMachineAnimation(finalResults) {
    const slotContents = document.querySelectorAll('.slot-content');
    slotContents.forEach((content, index) => {
        content.classList.remove('stopping', 'stopped');
        content.classList.add('spinning');

        // 添加随机滚动内容，传入最终结果
        animateSlotContent(content, index, finalResults);
    });
}

// 动画化老虎机内容 - 显示真实可选内容
function animateSlotContent(slotContent, index, finalResults) {
    const slotItem = slotContent.querySelector('.slot-item');
    const slotId = slotContent.id;

    // 获取这个slot对应的最终结果
    let finalResult = null;
    switch(slotId) {
        case 'map-result':
            finalResult = finalResults.map;
            break;
        case 'operator-result':
            finalResult = finalResults.operator;
            break;
        case 'helmet-result':
            finalResult = finalResults.helmet;
            break;
        case 'armor-result':
            finalResult = finalResults.armor;
            break;
        case 'backpack-result':
            finalResult = finalResults.backpack;
            break;
        case 'vest-result':
            finalResult = finalResults.vest;
            break;
        case 'weapon-result':
            finalResult = finalResults.weapon;
            break;
    }

    // 根据不同的slot类型获取可选内容
    let availableItems = [];

    switch(slotId) {
        case 'map-result':
            if (selectedMaps.length > 0) {
                availableItems = selectedMaps.map(mapKey => {
                    const [mapName, difficulty] = mapKey.split('-');
                    return {
                        name: `${mapName} - ${difficulty}`,
                        image: mapData[mapName].image
                    };
                });
            }
            break;
        case 'operator-result':
            if (selectedOperators.length > 0) {
                availableItems = selectedOperators.map(operatorName => {
                    const operator = operatorData.find(op => op.name === operatorName);
                    return operator;
                });
            }
            break;
        case 'helmet-result':
            availableItems = selectedEquipment.filter(item => item.category === '头盔');
            break;
        case 'armor-result':
            availableItems = selectedEquipment.filter(item => item.category === '护甲');
            break;
        case 'backpack-result':
            availableItems = selectedEquipment.filter(item => item.category === '背包');
            break;
        case 'vest-result':
            availableItems = selectedEquipment.filter(item => item.category === '胸挂');
            break;
        case 'weapon-result':
            console.log('处理weapon-result，selectedWeaponTypes:', selectedWeaponTypes);
            if (selectedWeaponTypes.length > 0) {
                availableItems = [];
                selectedWeaponTypes.forEach(weaponId => {
                    console.log('动画中处理weaponId:', weaponId);
                    const dashIndex = weaponId.indexOf('-');
                    const weaponType = weaponId.substring(0, dashIndex);
                    const weaponName = weaponId.substring(dashIndex + 1);
                    console.log('动画中解析结果 - 类型:', weaponType, '名称:', weaponName);
                    const weapons = weaponData[weaponType];
                    if (weapons && weapons.length > 0) {
                        const weapon = weapons.find(w => w.name === weaponName);
                        if (weapon) {
                            console.log('动画中找到枪械:', weapon);
                            availableItems.push(weapon);
                        } else {
                            console.log('动画中未找到枪械:', weaponName, '在类型', weaponType, '中');
                        }
                    } else {
                        console.log('动画中未找到枪械类型:', weaponType);
                    }
                });
                console.log('动画中availableItems:', availableItems);
            }
            break;
    }

    // 如果没有可选项，显示"未选择"
    if (availableItems.length === 0) {
        slotItem.innerHTML = '<div style="color: #666; font-size: 0.9rem;">未选择</div>';
        return; // 不参与滚动动画
    }

    // 开始滚动动画，显示真实的可选内容
    const interval = setInterval(() => {
        const randomItem = availableItems[Math.floor(Math.random() * availableItems.length)];

        // 添加淡入淡出效果
        slotItem.style.opacity = '0.3';

        setTimeout(() => {
            if (randomItem.image) {
                slotItem.innerHTML = `
                    <img src="${randomItem.image}" alt="${randomItem.name}" style="width: 60px; height: 60px; object-fit: cover; border-radius: 4px; margin-bottom: 3px;" onerror="this.style.display='none'">
                    <div style="font-size: 0.8rem; color: #FFD700;">${randomItem.name}</div>
                `;
            } else {
                slotItem.innerHTML = `<div style="font-size: 0.9rem; color: #FFD700;">${randomItem.name || randomItem}</div>`;
            }
            slotItem.style.opacity = '1';
        }, 20);
    }, 60); // 进一步减少间隔时间

    // 分别在不同时间停止每个老虎机，创造更真实的效果
    const stopTime = 2000 + (index * 300); // 每个老虎机延迟300ms停止，更快完成
    setTimeout(() => {
        clearInterval(interval);

        // 在停止前显示最终结果，避免跳变
        if (finalResult) {
            console.log('显示最终结果:', slotId, finalResult);
            if (finalResult.image) {
                slotItem.innerHTML = `
                    <img src="${finalResult.image}" alt="${finalResult.name}" style="width: 60px; height: 60px; object-fit: cover; border-radius: 4px; margin-bottom: 3px;" onerror="this.style.display='none'">
                    <div style="font-size: 0.8rem; color: #00ff41;">${finalResult.name}</div>
                `;
            } else {
                slotItem.innerHTML = `<div style="font-size: 0.9rem; color: #00ff41;">${finalResult.name || finalResult}</div>`;
            }
        } else {
            console.log('显示未选择:', slotId);
            slotItem.innerHTML = '<div style="color: #666; font-size: 0.9rem;">未选择</div>';
        }

        stopSlotAnimation(slotContent);
    }, stopTime);
}

// 停止单个老虎机动画
function stopSlotAnimation(slotContent) {
    slotContent.classList.remove('spinning');
    slotContent.classList.add('stopping');

    // 添加平滑的停止过渡
    const slotItem = slotContent.querySelector('.slot-item');
    slotItem.style.transition = 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)';

    setTimeout(() => {
        slotContent.classList.remove('stopping');
        slotContent.classList.add('stopped');

        // 添加最终确定的动画效果
        slotItem.style.transform = 'scale(1.05)';
        setTimeout(() => {
            slotItem.style.transform = 'scale(1)';
        }, 200);
    }, 300); // 减少停止时间
}

// 停止老虎机动画
function stopSlotMachineAnimation() {
    const slotContents = document.querySelectorAll('.slot-content');
    slotContents.forEach(content => {
        content.classList.remove('spinning', 'stopping');
        content.classList.add('stopped');
    });
}

// showResults函数已移除，现在动画结束时直接显示最终结果

// 更新结果显示
function updateResultDisplay(elementId, result, forceUpdate = false) {
    // 如果最终结果已显示且不是强制更新，则不更新
    if (finalResultsShown && !forceUpdate) {
        console.log(`跳过更新 ${elementId}，最终结果已显示`);
        return;
    }

    const element = document.getElementById(elementId);
    const slotItem = element.querySelector('.slot-item');

    if (result) {
        if (result.image) {
            slotItem.innerHTML = `
                <img src="${result.image}" alt="${result.name}" onerror="this.style.display='none'">
                <div>${result.name}</div>
            `;
        } else {
            slotItem.innerHTML = `<div>${result.name || result}</div>`;
        }
    } else {
        slotItem.innerHTML = '<div>未选择</div>';
    }
}

// 改进的随机数生成器 - 确保真正随机
function getSecureRandomIndex(arrayLength) {
    if (arrayLength === 0) return 0;

    // 使用更好的随机数生成
    const randomValue = Math.random();
    const index = Math.floor(randomValue * arrayLength);

    // 确保索引在有效范围内
    return Math.max(0, Math.min(index, arrayLength - 1));
}

// 防重复随机选择函数
function getRandomWithoutRepeat(items, historyKey, maxAttempts = 10) {
    if (!items || items.length === 0) return null;

    // 如果只有1-2个选项，无法避免重复，直接随机选择
    if (items.length <= 2) {
        const randomIndex = getSecureRandomIndex(items.length);
        const selected = items[randomIndex];
        updateDrawHistory(historyKey, selected);
        return selected;
    }

    let attempts = 0;
    let selected = null;

    while (attempts < maxAttempts) {
        const randomIndex = getSecureRandomIndex(items.length);
        selected = items[randomIndex];

        // 检查是否符合重复模式
        if (!isRepeatPattern(historyKey, selected)) {
            updateDrawHistory(historyKey, selected);
            return selected;
        }

        attempts++;
    }

    // 如果尝试多次仍然重复，强制选择符合规则的项目
    for (let item of items) {
        if (!isRepeatPattern(historyKey, item)) {
            updateDrawHistory(historyKey, item);
            return item;
        }
    }

    // 最后的保险，直接返回第一个不同的项目
    updateDrawHistory(historyKey, selected);
    return selected;
}

// 检查是否会造成不允许的重复模式
function isRepeatPattern(historyKey, newItem) {
    const history = drawHistory[historyKey];
    const newItemKey = getItemKey(newItem);

    // 防止连续两次相同 (A-A)
    if (history.length >= 1) {
        const lastItemKey = getItemKey(history[history.length - 1]);
        if (newItemKey === lastItemKey) {
            return true; // 连续两次相同
        }
    }

    // 防止隔一次抽中相同 (A-B-A)
    if (history.length >= 2) {
        const secondLastItemKey = getItemKey(history[history.length - 2]);
        if (newItemKey === secondLastItemKey) {
            return true; // 隔一次相同
        }
    }

    return false;
}

// 获取项目的唯一标识
function getItemKey(item) {
    if (!item) return null;
    if (typeof item === 'string') return item;
    return item.name || item.id || JSON.stringify(item);
}

// 更新抽取历史
function updateDrawHistory(historyKey, item) {
    if (!drawHistory[historyKey]) {
        drawHistory[historyKey] = [];
    }

    drawHistory[historyKey].push(item);

    // 只保留最近3次记录
    if (drawHistory[historyKey].length > 3) {
        drawHistory[historyKey].shift();
    }
}

// 随机性测试函数 - 用于验证抽取是否真正随机
function testRandomness(testCount = 100) {
    console.log(`=== 开始随机性测试 (${testCount}次) ===`);

    if (selectedMaps.length > 0) {
        const mapResults = {};
        for (let i = 0; i < testCount; i++) {
            const result = getRandomMap();
            if (result) {
                mapResults[result.name] = (mapResults[result.name] || 0) + 1;
            }
        }
        console.log('地图抽取分布:', mapResults);

        // 计算期望概率
        const expectedProbability = (100 / selectedMaps.length).toFixed(1);
        console.log(`地图期望概率: 每个地图约${expectedProbability}%`);
    }

    if (selectedOperators.length > 0) {
        const operatorResults = {};
        for (let i = 0; i < testCount; i++) {
            const result = getRandomOperator();
            if (result) {
                operatorResults[result.name] = (operatorResults[result.name] || 0) + 1;
            }
        }
        console.log('干员抽取分布:', operatorResults);

        const expectedProbability = (100 / selectedOperators.length).toFixed(1);
        console.log(`干员期望概率: 每个干员约${expectedProbability}%`);
    }

    console.log('=== 随机性测试完成 ===');
    console.log('如果分布相对均匀，说明随机性良好');
}

// 在控制台暴露测试函数
window.testRandomness = testRandomness;

// 随机选择地图 - 防重复版本
function getRandomMap() {
    if (selectedMaps.length === 0) return null;

    const mapItems = selectedMaps.map(mapKey => {
        const [mapName, difficulty] = mapKey.split('-');
        return {
            name: `${mapName} - ${difficulty}`,
            image: mapData[mapName].image,
            key: mapKey
        };
    });

    return getRandomWithoutRepeat(mapItems, 'map');
}

// 随机选择干员 - 防重复版本
function getRandomOperator() {
    if (selectedOperators.length === 0) return null;

    const operatorItems = selectedOperators.map(operatorName => {
        return operatorData.find(op => op.name === operatorName);
    }).filter(op => op !== undefined);

    return getRandomWithoutRepeat(operatorItems, 'operator');
}

// 按装备类型随机选择装备 - 防重复版本
function getRandomEquipmentByType(equipmentType) {
    // 过滤出指定类型的装备
    const typeEquipment = selectedEquipment.filter(item => item.category === equipmentType);

    if (typeEquipment.length === 0) return null;

    const equipmentItems = typeEquipment.map(item => ({
        name: item.name,
        image: item.image,
        quality: item.quality
    }));

    // 根据装备类型使用不同的历史记录键
    const historyKey = equipmentType === '头盔' ? 'helmet' :
                      equipmentType === '护甲' ? 'armor' :
                      equipmentType === '背包' ? 'backpack' :
                      equipmentType === '胸挂' ? 'vest' : 'equipment';

    return getRandomWithoutRepeat(equipmentItems, historyKey);
}

// 随机选择装备（保留兼容性）
function getRandomEquipment() {
    if (selectedEquipment.length === 0) return null;

    const randomIndex = getSecureRandomIndex(selectedEquipment.length);
    const selectedItem = selectedEquipment[randomIndex];

    return {
        name: `${selectedItem.name} (${selectedItem.category})`,
        image: selectedItem.image,
        quality: selectedItem.quality
    };
}

// 随机选择枪械 - 防重复版本
function getRandomWeapon() {
    if (selectedWeaponTypes.length === 0) return null;

    // 收集所有选中的具体枪械
    const availableWeapons = [];

    selectedWeaponTypes.forEach(weaponId => {
        console.log('处理weaponId:', weaponId);

        // weaponId 格式为 "类型-枪械名称"，例如 "手枪-G17"
        const parts = weaponId.split('-');
        const weaponType = parts[0];
        const weaponName = parts.slice(1).join('-'); // 处理名称中包含'-'的情况

        console.log('解析结果 - 类型:', weaponType, '名称:', weaponName);

        const weapons = weaponData[weaponType];
        console.log('该类型的所有枪械:', weapons ? weapons.length : 0);

        if (weapons && weapons.length > 0) {
            const weapon = weapons.find(w => w.name === weaponName);
            console.log('找到匹配的枪械:', weapon);
            if (weapon) {
                availableWeapons.push(weapon);
            }
        } else {
            console.log('未找到类型:', weaponType, '在weaponData中');
        }
    });

    if (availableWeapons.length === 0) return null;

    return getRandomWithoutRepeat(availableWeapons, 'weapon');
}

// 初始化全选按钮功能
function initializeSelectAllButtons() {
    // 地图全选按钮
    const selectAllMapsBtn = document.getElementById('select-all-maps');
    selectAllMapsBtn.addEventListener('click', () => selectAllItems('map'));

    // 地图取消全选按钮
    const deselectAllMapsBtn = document.getElementById('deselect-all-maps');
    deselectAllMapsBtn.addEventListener('click', () => deselectAllItems('map'));

    // 干员全选按钮
    const selectAllOperatorsBtn = document.getElementById('select-all-operators');
    selectAllOperatorsBtn.addEventListener('click', () => selectAllItems('operator'));

    // 干员取消全选按钮
    const deselectAllOperatorsBtn = document.getElementById('deselect-all-operators');
    deselectAllOperatorsBtn.addEventListener('click', () => deselectAllItems('operator'));

    // 枪械全选按钮
    const selectAllWeaponsBtn = document.getElementById('select-all-weapons');
    selectAllWeaponsBtn.addEventListener('click', () => selectAllItems('weapon'));

    // 枪械取消全选按钮
    const deselectAllWeaponsBtn = document.getElementById('deselect-all-weapons');
    deselectAllWeaponsBtn.addEventListener('click', () => deselectAllItems('weapon'));

    // 装备全选按钮
    const selectAllHelmetsBtn = document.getElementById('select-all-helmets');
    selectAllHelmetsBtn.addEventListener('click', () => selectAllItems('helmet'));

    // 头盔取消全选按钮
    const deselectAllHelmetsBtn = document.getElementById('deselect-all-helmets');
    deselectAllHelmetsBtn.addEventListener('click', () => deselectAllItems('helmet'));

    const selectAllArmorBtn = document.getElementById('select-all-armor');
    selectAllArmorBtn.addEventListener('click', () => selectAllItems('armor'));

    // 护甲取消全选按钮
    const deselectAllArmorBtn = document.getElementById('deselect-all-armor');
    deselectAllArmorBtn.addEventListener('click', () => deselectAllItems('armor'));

    const selectAllBackpacksBtn = document.getElementById('select-all-backpacks');
    selectAllBackpacksBtn.addEventListener('click', () => selectAllItems('backpack'));

    // 背包取消全选按钮
    const deselectAllBackpacksBtn = document.getElementById('deselect-all-backpacks');
    deselectAllBackpacksBtn.addEventListener('click', () => deselectAllItems('backpack'));

    const selectAllVestsBtn = document.getElementById('select-all-vests');
    selectAllVestsBtn.addEventListener('click', () => selectAllItems('vest'));

    // 胸挂取消全选按钮
    const deselectAllVestsBtn = document.getElementById('deselect-all-vests');
    deselectAllVestsBtn.addEventListener('click', () => deselectAllItems('vest'));
}

// 全选功能实现
function selectAllItems(category) {
    console.log(`全选 ${category} 类目`);

    switch(category) {
        case 'map':
            selectAllMaps();
            break;
        case 'operator':
            selectAllOperators();
            break;
        case 'weapon':
            selectAllWeapons();
            break;
        case 'helmet':
            selectAllEquipmentByType('helmet');
            break;
        case 'armor':
            selectAllEquipmentByType('armor');
            break;
        case 'backpack':
            selectAllEquipmentByType('backpack');
            break;
        case 'vest':
            selectAllEquipmentByType('vest');
            break;
    }
}

// 取消全选功能实现
function deselectAllItems(category) {
    console.log(`取消全选 ${category} 类目`);

    switch(category) {
        case 'map':
            deselectAllMaps();
            break;
        case 'operator':
            deselectAllOperators();
            break;
        case 'weapon':
            deselectAllWeapons();
            break;
        case 'helmet':
            deselectAllEquipmentByType('helmet');
            break;
        case 'armor':
            deselectAllEquipmentByType('armor');
            break;
        case 'backpack':
            deselectAllEquipmentByType('backpack');
            break;
        case 'vest':
            deselectAllEquipmentByType('vest');
            break;
    }
}

// 全选地图
function selectAllMaps() {
    const mapItems = document.querySelectorAll('.map-item');
    selectedMaps = [];

    mapItems.forEach(item => {
        item.classList.add('selected');
        const mapName = item.dataset.map;
        const difficulty = item.dataset.difficulty;
        // 使用与原有逻辑兼容的格式：mapName-difficulty
        selectedMaps.push(`${mapName}-${difficulty}`);
    });

    console.log('已选择所有地图:', selectedMaps);
}

// 全选干员
function selectAllOperators() {
    const operatorItems = document.querySelectorAll('.operator-item');
    selectedOperators = [];

    operatorItems.forEach(item => {
        item.classList.add('selected');
        const operatorName = item.dataset.operator;
        selectedOperators.push(operatorName);
    });

    console.log('已选择所有干员:', selectedOperators);
}

// 全选枪械
function selectAllWeapons() {
    const weaponItems = document.querySelectorAll('.weapon-item');
    selectedWeaponTypes = [];

    weaponItems.forEach(item => {
        item.classList.add('selected');
        const weaponId = item.dataset.weaponId;
        selectedWeaponTypes.push(weaponId);
    });

    console.log('已选择所有枪械:', selectedWeaponTypes);
}

// 全选指定类型的装备
function selectAllEquipmentByType(equipmentType) {
    const gridId = `${equipmentType}-grid`;
    const equipmentItems = document.querySelectorAll(`#${gridId} .equipment-item`);

    // 映射英文类型到中文类型（与抽取逻辑兼容）
    const typeMapping = {
        'helmet': '头盔',
        'armor': '护甲',
        'backpack': '背包',
        'vest': '胸挂'
    };

    const chineseType = typeMapping[equipmentType] || equipmentType;

    // 清除该类型装备的现有选择（使用正确的category字段）
    selectedEquipment = selectedEquipment.filter(item => item.category !== chineseType);

    // 获取装备数据
    const equipmentList = equipmentData[chineseType];
    if (!equipmentList) return;

    equipmentItems.forEach((item) => {
        item.classList.add('selected');
        const equipmentId = item.dataset.equipmentId;

        // 从equipmentId中解析装备名称
        const equipmentName = equipmentId.split('-').slice(1).join('-'); // 处理名称中可能包含'-'的情况

        // 从装备数据中找到对应的装备信息
        const equipment = equipmentList.find(eq => eq.name === equipmentName);
        if (equipment) {
            // 检查是否已经存在，避免重复添加
            const existingIndex = selectedEquipment.findIndex(item => item.id === equipmentId);
            if (existingIndex === -1) {
                selectedEquipment.push({
                    id: equipmentId,
                    name: equipment.name,
                    quality: equipment.quality,
                    image: equipment.image,
                    category: chineseType // 使用中文类型
                });
            }
        }
    });

    console.log(`已选择所有${equipmentType}:`, selectedEquipment.filter(item => item.category === chineseType));
    console.log('选中的装备:', selectedEquipment);
}

// 取消全选地图
function deselectAllMaps() {
    const mapItems = document.querySelectorAll('.map-item');
    selectedMaps = [];

    mapItems.forEach(item => {
        item.classList.remove('selected');
    });

    console.log('已取消选择所有地图');
}

// 取消全选干员
function deselectAllOperators() {
    const operatorItems = document.querySelectorAll('.operator-item');
    selectedOperators = [];

    operatorItems.forEach(item => {
        item.classList.remove('selected');
    });

    console.log('已取消选择所有干员');
}

// 取消全选枪械
function deselectAllWeapons() {
    const weaponItems = document.querySelectorAll('.weapon-item');
    selectedWeaponTypes = [];

    weaponItems.forEach(item => {
        item.classList.remove('selected');
    });

    console.log('已取消选择所有枪械');
}

// 按类别全选枪械
function selectAllWeaponsByCategory(category) {
    const categoryMap = {
        '手枪': 'pistol-grid',
        '冲锋枪': 'smg-grid',
        '步枪': 'rifle-grid',
        '射手步枪': 'marksman-grid',
        '狙击步枪': 'sniper-grid',
        '机枪': 'lmg-grid',
        '霰弹枪': 'shotgun-grid'
    };

    const gridId = categoryMap[category];
    if (!gridId) return;

    const weaponItems = document.querySelectorAll(`#${gridId} .weapon-item`);

    weaponItems.forEach(item => {
        if (!item.classList.contains('selected')) {
            item.classList.add('selected');
            const weaponId = item.dataset.weaponId;
            if (!selectedWeaponTypes.includes(weaponId)) {
                selectedWeaponTypes.push(weaponId);
            }
        }
    });

    console.log(`已选择所有${category}:`, selectedWeaponTypes);
}

// 按类别取消全选枪械
function deselectAllWeaponsByCategory(category) {
    const categoryMap = {
        '手枪': 'pistol-grid',
        '冲锋枪': 'smg-grid',
        '步枪': 'rifle-grid',
        '射手步枪': 'marksman-grid',
        '狙击步枪': 'sniper-grid',
        '机枪': 'lmg-grid',
        '霰弹枪': 'shotgun-grid'
    };

    const gridId = categoryMap[category];
    if (!gridId) return;

    const weaponItems = document.querySelectorAll(`#${gridId} .weapon-item`);

    weaponItems.forEach(item => {
        if (item.classList.contains('selected')) {
            item.classList.remove('selected');
            const weaponId = item.dataset.weaponId;
            selectedWeaponTypes = selectedWeaponTypes.filter(id => id !== weaponId);
        }
    });

    console.log(`已取消选择所有${category}:`, selectedWeaponTypes);
}

// 取消全选指定类型的装备
function deselectAllEquipmentByType(equipmentType) {
    const gridId = `${equipmentType}-grid`;
    const equipmentItems = document.querySelectorAll(`#${gridId} .equipment-item`);

    // 映射英文类型到中文类型（与抽取逻辑兼容）
    const typeMapping = {
        'helmet': '头盔',
        'armor': '护甲',
        'backpack': '背包',
        'vest': '胸挂'
    };

    const chineseType = typeMapping[equipmentType] || equipmentType;

    // 清除该类型装备的现有选择
    selectedEquipment = selectedEquipment.filter(item => item.category !== chineseType);

    equipmentItems.forEach(item => {
        item.classList.remove('selected');
    });

    console.log(`已取消选择所有${equipmentType}`);
    console.log('选中的装备:', selectedEquipment);
}
